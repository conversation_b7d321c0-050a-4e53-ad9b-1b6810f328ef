// 钉钉应用配置
export const DINGTALK_CONFIG = {
  // 钉钉应用的 AppKey (Client ID)
  APP_KEY: 'dinghtrzwqcet72jfjgw',
  
  // 钉钉应用的 AppSecret (Client Secret)
  APP_SECRET: 'sKQB_p3SxOctg50qTQqtTOwv8WEEJnm81AFKrA27edIxJL0mp3Pj9j8ligIcihTG',
  
  // 回调地址 (需要在钉钉开放平台配置)
  REDIRECT_URI: process.env.NEXT_PUBLIC_DINGTALK_REDIRECT_URI || 'http://localhost:3000/login',
  
  // 钉钉内嵌二维码登录地址
  QRCONNECT_URL: 'https://login.dingtalk.com/sns/qrconnect',
  
  // 旧版网页授权地址 (H5微应用)
  OAUTH_URL: 'https://login.dingtalk.com/oauth2/auth',
  
  // 获取用户信息的API地址
  USER_INFO_URL: 'https://api.dingtalk.com/v1.0/contact/users/me',
  
  // 获取访问令牌的API地址
  ACCESS_TOKEN_URL: 'https://api.dingtalk.com/v1.0/oauth2/userAccessToken',
};

// 钉钉用户信息接口
export interface DingTalkUserInfo {
  nick: string;         // 用户昵称
  openid: string;       // 用户openid
  unionid: string;      // 用户unionid
  dingId?: string;      // 用户dingId
}
