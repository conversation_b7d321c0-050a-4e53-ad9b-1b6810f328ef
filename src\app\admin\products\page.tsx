'use client';

import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { mockCategories, mockProducts } from '@/data/mockData';
import { Product } from '@/types';
import Image from 'next/image';

const { Option } = Select;

export default function ProductManagePage() {
  const [user, setUser] = useState(null);
  const [products, setProducts] = useState(mockProducts);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('products');
  const [form] = Form.useForm();
  const router = useRouter();

  useEffect(() => {
    // 检查用户登录状态
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }
    setUser(JSON.parse(userData));
  }, [router]);

  // 如果用户未登录，不渲染页面
  if (!user) {
    return null;
  }

  const handleCategoryClick = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleBackToHome = () => {
    router.push('/');
  };

  const handleBackToAdmin = () => {
    router.push('/admin');
  };

  const columns = [
    {
      title: '产品图片',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl: string) => (
        <Image
          src={imageUrl}
          alt="产品图片"
          width={200}
          height={200}
          className="w-16 h-12 object-cover rounded-lg"
        />
      ),
    },
    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      key: 'categoryId',
      render: (categoryId: string) => {
        const category = mockCategories.find(cat => cat.id === categoryId);
        return (
          <Tag color="blue">
            {category?.name || '未知分类'}
          </Tag>
        );
      },
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: Date) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: unknown, record: Product) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    form.setFieldsValue(product);
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个产品吗？',
      onOk() {
        setProducts(products.filter(p => p.id !== id));
        message.success('删除成功');
      },
    });
  };

  const handleSubmit = async (values: Partial<Product>) => {
    try {
      if (editingProduct) {
        // 编辑产品
        setProducts(products.map(p => 
          p.id === editingProduct.id 
            ? { ...p, ...values }
            : p
        ));
        message.success('更新成功');
      } else {
        // 添加产品
        const newProduct: Product = {
          id: Date.now().toString(),
          name: values.name || '',
          imageUrl: values.imageUrl || '',
          categoryId: values.categoryId || '',
          sortOrder: values.sortOrder || 0,
          createdAt: new Date(),
        };
        setProducts([...products, newProduct]);
        message.success('添加成功');
      }
      setIsModalVisible(false);
    } catch {
      message.error('操作失败');
    }
  };

  return (
    <div className="relative min-h-screen font-sans overflow-hidden" style={{ backgroundColor: '#f8fafc' }}>
      {/* Background decorative shapes - 动态背景装饰 */}
      <div className="absolute top-0 -left-20 w-96 h-96 bg-yellow-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob"></div>
      <div className="absolute top-0 -right-20 w-96 h-96 bg-teal-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-2000"></div>
      <div className="absolute -bottom-20 left-1/3 w-96 h-96 bg-slate-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-4000"></div>

      <div className="flex h-screen relative z-10">
        {/* Left Sidebar Navigation - 清晰侧边栏 */}
        <aside className="w-64 bg-white border-r border-gray-200 flex-col fixed inset-y-0 left-0 hidden md:flex shadow-xl">
          <div className="h-16 flex items-center justify-center text-xl font-bold border-b border-gray-200/50 text-slate-800">
            <i className="fas fa-bolt-lightning text-yellow-400 mr-2"></i>
            <span>秒报价系统</span>
          </div>
          <nav className="flex-1 px-4 py-6 space-y-1">
            <a
              href="#"
              onClick={handleBackToHome}
              className={`flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-slate-600 hover:bg-slate-50/80 hover:text-slate-800`}
            >
              <i className="fas fa-th-large w-5 h-5 mr-3"></i>
              <span>秒报价首页</span>
            </a>
            {mockCategories.map((category) => (
              <a
                key={category.id}
                href="#"
                onClick={() => handleCategoryClick(category.id)}
                className={`flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-slate-600 hover:bg-slate-50/80 hover:text-slate-800`}
              >
                <i className={`fas ${getCategoryIcon(category.name)} w-5 h-5 mr-3`}></i>
                <span>{category.name}</span>
              </a>
            ))}
            <a
              href="#"
              onClick={handleBackToAdmin}
              className="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-50/80 hover:text-slate-800 transition-all duration-200"
            >
              <i className="fas fa-cog w-5 h-5 mr-3"></i>
              <span>系统设置</span>
            </a>
            <a
              href="#"
              className="flex items-center px-4 py-2.5 text-sm font-semibold rounded-lg bg-slate-100/80 text-slate-800 shadow-sm ml-4"
            >
              <i className="fas fa-box w-5 h-5 mr-3"></i>
              <span>产品管理</span>
            </a>
          </nav>
          <div className="p-4 border-t border-gray-200/50">
            <button className="w-full flex items-center justify-center text-sm text-slate-500 hover:text-slate-700 transition-colors duration-200">
              <i className="fas fa-bars"></i>
            </button>
          </div>
        </aside>

        {/* Main Content Area - 产品管理内容 */}
        <main className="flex-1 md:ml-64 p-6 lg:p-8 overflow-y-auto">
          <header className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <button
                onClick={handleBackToAdmin}
                className="mr-4 p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-all duration-200"
              >
                <i className="fas fa-arrow-left"></i>
              </button>
              <h1 className="text-2xl font-bold text-slate-800">产品管理</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleAdd}
                className="flex items-center px-4 py-2 bg-slate-800 text-white rounded-lg hover:bg-slate-900 transition-colors duration-200 shadow-sm"
              >
                <i className="fas fa-plus mr-2"></i>
                添加产品
              </button>
            </div>
          </header>

          {/* Product Management Section */}
          <section className="bg-white p-6 lg:p-8 rounded-2xl shadow-xl border border-gray-200">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-slate-800 flex items-center mb-2">
                <i className="fas fa-box mr-3 text-yellow-400"></i>
                产品列表
              </h2>
              <p className="text-slate-600 text-sm">管理系统中的所有产品信息</p>
            </div>

            <Table
              columns={columns}
              dataSource={products}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              className="admin-table"
            />
          </section>
        </main>
      </div>

      <Modal
        title={editingProduct ? '编辑产品' : '添加产品'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
        className="admin-modal"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="产品名称"
            rules={[{ required: true, message: '请输入产品名称' }]}
          >
            <Input placeholder="请输入产品名称" className="rounded-lg" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="产品分类"
            rules={[{ required: true, message: '请选择产品分类' }]}
          >
            <Select placeholder="请选择产品分类" className="rounded-lg">
              {mockCategories.map(category => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="imageUrl"
            label="产品图片"
            rules={[{ required: true, message: '请输入图片URL' }]}
          >
            <Input placeholder="请输入图片URL" className="rounded-lg" />
          </Form.Item>

          <Form.Item
            name="sortOrder"
            label="排序"
            rules={[{ required: true, message: '请输入排序号' }]}
          >
            <Input type="number" placeholder="请输入排序号" className="rounded-lg" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

// 获取分类图标的辅助函数
function getCategoryIcon(categoryName: string): string {
  switch (categoryName) {
    case '无纺布袋':
      return 'fa-shopping-bag';
    case '塑料袋':
      return 'fa-box';
    case '其他袋形':
      return 'fa-shapes';
    default:
      return 'fa-folder';
  }
}
