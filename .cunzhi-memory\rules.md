# 开发规范和规则

- 修复了Header和Sidebar的样式问题：1. Header设置为固定定位(fixed top-0)，防止滚动时跟随；2. 强制设置文字颜色为深灰色(!text-gray-800)确保可见性；3. 调整布局为Header留出64px空间(pt-16)；4. Content区域设置左边距280px适应Sidebar宽度
- 用户反馈的关键问题：1. 产品图片全部显示为白色方框，图片加载失败；2. 左侧菜单栏文字是黑色看不清楚；3. 产品布局不均衡（第一行6个，第二行1个）；4. 图片内嵌文字与下方标题不一致；5. 缺乏交互反馈和视觉引导
- 首页产品图片尺寸规范：width={80} height={80}，网格布局为 grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-9 2xl:grid-cols-10，间距为 gap-x-3 gap-y-4。此规范已确定，后续不再修改。
- 首页产品图片尺寸规范更新：Next.js Image组件使用 width={200} height={200}（匹配实际图片尺寸），网格布局为 grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-9 2xl:grid-cols-10，间距为 gap-x-3 gap-y-4。此规范已确定，后续不再修改。
