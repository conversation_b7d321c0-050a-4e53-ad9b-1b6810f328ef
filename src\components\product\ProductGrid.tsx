'use client';

import React from 'react';
import { Row, Col, Typography, Empty } from 'antd';
import { Product, Category } from '@/types';
import ProductCard from './ProductCard';

const { Title } = Typography;

interface ProductGridProps {
  products: Product[];
  categories: Category[];
  onProductClick?: (product: Product) => void;
}

export default function ProductGrid({ products, categories, onProductClick }: ProductGridProps) {
  // 按分类分组产品
  const productsByCategory = categories.map(category => ({
    category,
    products: products.filter(product => product.categoryId === category.id),
  }));

  if (products.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Empty description="暂无产品数据" />
      </div>
    );
  }

  return (
    <div className="space-y-12">
      {productsByCategory.map(({ category, products: categoryProducts }) => {
        if (categoryProducts.length === 0) return null;

        return (
          <section key={category.id} className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-1 h-8 bg-blue-500 rounded-full"></div>
              <Title level={3} className="mb-0 text-gray-800">
                {category.name}
              </Title>
            </div>
            
            <Row gutter={[24, 24]} justify="start">
              {categoryProducts.map(product => (
                <Col
                  key={product.id}
                  xs={12}
                  sm={12}
                  md={8}
                  lg={6}
                  xl={4}
                  xxl={4}
                >
                  <ProductCard
                    product={product}
                    onClick={onProductClick}
                  />
                </Col>
              ))}
            </Row>
          </section>
        );
      })}
    </div>
  );
}
