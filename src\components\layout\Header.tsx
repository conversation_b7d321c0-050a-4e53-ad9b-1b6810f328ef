'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Avatar, Dropdown, Button, Space, Typography } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined, MenuOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { User } from '@/types';

const { Header: AntHeader } = Layout;
const { Text } = Typography;

interface HeaderProps {
  onMenuToggle?: () => void;
  collapsed?: boolean;
}

export default function Header({ onMenuToggle }: HeaderProps) {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    // 从localStorage获取用户信息
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('user');
    router.push('/login');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <AntHeader className="bg-white shadow-sm border-b border-gray-200 px-6 flex items-center justify-between fixed top-0 left-0 right-0 z-50">
      <div className="flex items-center space-x-4">
        <Button
          type="text"
          icon={<MenuOutlined />}
          onClick={onMenuToggle}
          className="lg:hidden text-gray-800"
        />
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <Text className="text-white font-bold text-sm">报</Text>
          </div>
          <div>
            <Text className="text-lg font-semibold text-gray-800 !text-gray-800">
              报价器系统
            </Text>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {user ? (
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors">
              <Avatar
                size="small"
                src={user.avatar}
                icon={<UserOutlined />}
              />
              <Space>
                <Text className="text-gray-700 !text-gray-700">{user.name}</Text>
                <Text type="secondary" className="text-xs !text-gray-500">
                  {user.role === 'admin' ? '管理员' : '用户'}
                </Text>
              </Space>
            </div>
          </Dropdown>
        ) : (
          <Button type="primary" onClick={() => router.push('/login')}>
            登录
          </Button>
        )}
      </div>
    </AntHeader>
  );
}
