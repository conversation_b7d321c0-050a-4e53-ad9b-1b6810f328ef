<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>钉钉登录</title>
    <script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #ffffff;
        }
        #login_container {
            width: 180px;
            height: 200px;
        }
    </style>
</head>
<body>
    <div id="login_container"></div>
    <script>
        window.onload = function() {
            console.log('钉钉登录页面加载完成');
            
            if (typeof DDLogin !== 'undefined') {
                console.log('钉钉SDK已加载，开始初始化');
                
                try {
                    const redirectUri = encodeURIComponent('http://localhost:3000/login');
                    const gotoUrl = encodeURIComponent(
                        'https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=dinghtrzwqcet72jfjgw&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=' + redirectUri
                    );
                    
                    DDLogin({
                        id: 'login_container',
                        goto: gotoUrl,
                        style: 'border:none;background-color:#FFFFFF;',
                        width: '180',
                        height: '200'
                    });
                    
                    console.log('钉钉二维码初始化完成');
                } catch (error) {
                    console.error('钉钉登录初始化失败:', error);
                    document.getElementById('login_container').innerHTML = '<div style="text-align:center;padding:50px;color:#666;">二维码加载失败<br><button onclick="location.reload()">重新加载</button></div>';
                }
            } else {
                console.error('钉钉SDK未加载');
                document.getElementById('login_container').innerHTML = '<div style="text-align:center;padding:50px;color:#666;">SDK加载失败<br><button onclick="location.reload()">重新加载</button></div>';
            }
        };
    </script>
</body>
</html>
