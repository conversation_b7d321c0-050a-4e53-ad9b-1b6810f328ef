<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 秒报价系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            background-color: #f8fafc;
        }
        /* Define the animation for the blobs */
        @keyframes blob-animation {
            0% {
                transform: scale(1) translate(0, 0);
            }
            33% {
                transform: scale(1.1) translate(20px, -30px);
            }
            66% {
                transform: scale(0.9) translate(-20px, 20px);
            }
            100% {
                transform: scale(1) translate(0, 0);
            }
        }
        .animate-blob {
            animation: blob-animation 15s infinite ease-in-out;
        }
        .animation-delay-2000 {
            animation-delay: -2s;
        }
        .animation-delay-4000 {
            animation-delay: -4s;
        }
    </style>
</head>
<body class="font-sans relative min-h-screen flex items-center justify-center overflow-hidden">

    <!-- Background decorative shapes -->
    <div class="absolute top-0 -left-20 w-96 h-96 bg-yellow-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob"></div>
    <div class="absolute top-0 -right-20 w-96 h-96 bg-teal-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-20 left-1/3 w-96 h-96 bg-slate-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-4000"></div>

    <!-- Main Login Container -->
    <div class="relative z-10 w-full max-w-md p-4">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8">
            
            <div class="text-center mb-10">
                <div class="inline-flex items-center text-3xl font-bold text-gray-800">
                    <i class="fas fa-bolt-lightning text-yellow-400 mr-3"></i>
                    <span>秒报价系统</span>
                </div>
                <p class="text-gray-500 text-sm mt-2">欢迎回来，请登录</p>
            </div>

            <!-- Tab Buttons -->
            <div class="flex justify-center border-b mb-8">
                <button id="scan-tab" class="flex-1 pb-3 text-center font-semibold text-slate-800 border-b-2 border-slate-800">扫码登录</button>
                <button id="pwd-tab" class="flex-1 pb-3 text-center font-semibold text-gray-400 border-b-2 border-transparent">账号登录</button>
            </div>

            <!-- Scan to Login View -->
            <div id="scan-view">
                <div class="relative w-56 h-56 mx-auto border-4 border-gray-100 rounded-xl p-1 shadow-inner">
                    <img src="https://placehold.co/224x224/ffffff/334155?text=请刷新二维码" alt="钉钉登录二维码" class="w-full h-full rounded-md">
                    <div id="qrcode-refresh" class="absolute inset-0 bg-black bg-opacity-60 flex flex-col items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity cursor-pointer rounded-xl">
                        <i class="fas fa-sync-alt text-3xl"></i>
                        <span class="text-base mt-2">刷新</span>
                    </div>
                </div>
                <p class="text-center text-gray-500 mt-6">
                    请使用 <span class="text-slate-800 font-semibold">钉钉</span> 扫描二维码登录
                </p>
            </div>

            <!-- Account Login View (Hidden by default) -->
            <div id="pwd-view" class="hidden">
                <form class="space-y-6">
                    <div>
                        <div class="relative">
                             <i class="fas fa-user absolute left-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                            <input id="username" name="username" type="text" required class="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500" placeholder="请输入您的账号">
                        </div>
                    </div>
                    <div>
                        <div class="relative">
                            <i class="fas fa-lock absolute left-4 top-1/2 -translate-y-1/2 text-gray-400"></i>
                            <input id="password" name="password" type="password" required class="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500" placeholder="请输入您的密码">
                        </div>
                    </div>
                    <div class="flex items-center justify-end text-sm">
                        <a href="#" class="font-medium text-slate-600 hover:text-slate-900">忘记密码?</a>
                    </div>
                    <div>
                        <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-slate-800 hover:bg-slate-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-transform hover:scale-105">
                            登 录
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <footer class="text-center mt-8">
            <p class="text-xs text-gray-500">版权所有：杭州有限公司 www.maibaodai.cn</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const scanTab = document.getElementById('scan-tab');
            const pwdTab = document.getElementById('pwd-tab');
            const scanView = document.getElementById('scan-view');
            const pwdView = document.getElementById('pwd-view');

            scanTab.addEventListener('click', () => {
                scanView.classList.remove('hidden');
                pwdView.classList.add('hidden');
                
                scanTab.classList.add('text-slate-800', 'border-slate-800');
                scanTab.classList.remove('text-gray-400', 'border-transparent');
                
                pwdTab.classList.add('text-gray-400', 'border-transparent');
                pwdTab.classList.remove('text-slate-800', 'border-slate-800');
            });

            pwdTab.addEventListener('click', () => {
                pwdView.classList.remove('hidden');
                scanView.classList.add('hidden');

                pwdTab.classList.add('text-slate-800', 'border-slate-800');
                pwdTab.classList.remove('text-gray-400', 'border-transparent');

                scanTab.classList.add('text-gray-400', 'border-transparent');
                scanTab.classList.remove('text-slate-800', 'border-slate-800');
            });

            const qrRefresh = document.getElementById('qrcode-refresh');
            qrRefresh.addEventListener('click', () => {
                const qrImage = qrRefresh.previousElementSibling;
                const icon = qrRefresh.querySelector('i');
                
                icon.classList.add('animate-spin');
                
                setTimeout(() => {
                    const newTimestamp = new Date().getTime();
                    qrImage.src = `https://placehold.co/224x224/ffffff/334155?text=QR+${newTimestamp}`;
                    icon.classList.remove('animate-spin');
                }, 1000);
            });
        });
    </script>

</body>
</html>
