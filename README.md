# 报价器系统

基于 Next.js 14 开发的现代化报价系统，支持钉钉扫码登录和后台管理功能。

## 功能特性

- 🔐 钉钉扫码登录
- 📱 响应式设计
- 🎨 现代化UI界面
- 📊 产品分类展示
- ⚙️ 后台管理系统
- 🖼️ 图片上传管理

## 技术栈

- **前端框架**: Next.js 14 + TypeScript
- **样式方案**: Tailwind CSS + Ant Design
- **状态管理**: React Hooks
- **数据库**: MySQL
- **身份验证**: JWT + 钉钉开放平台
- **图片存储**: 阿里云OSS

## 项目结构

```
├── src/
│   ├── app/                 # Next.js 13+ App Router
│   │   ├── login/          # 登录页面
│   │   ├── admin/          # 后台管理
│   │   └── page.tsx        # 首页
│   ├── components/         # 公共组件
│   │   ├── ui/            # UI组件
│   │   ├── layout/        # 布局组件
│   │   └── product/       # 产品相关组件
│   ├── lib/               # 工具库
│   ├── types/             # TypeScript类型定义
│   └── data/              # 模拟数据
├── public/                # 静态资源
└── docs/                  # 文档
```

## 快速开始

1. 安装依赖
```bash
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

3. 打开浏览器访问 http://localhost:3000

## 开发计划

### Phase 1: 基础框架 ✅
- [x] 项目初始化
- [x] UI框架搭建
- [x] 基础组件开发

### Phase 2: 核心功能
- [ ] 钉钉登录集成
- [ ] 产品展示功能
- [ ] 数据库设计

### Phase 3: 管理功能
- [ ] 后台管理界面
- [ ] 产品管理
- [ ] 用户权限管理

### Phase 4: 优化部署
- [ ] 性能优化
- [ ] 生产环境部署
- [ ] 监控和日志

## 环境变量

创建 `.env.local` 文件：

```env
# 钉钉配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret

# 数据库配置
DATABASE_URL=mysql://username:password@localhost:3306/quotation_system

# JWT密钥
JWT_SECRET=your_jwt_secret

# 阿里云OSS配置
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_BUCKET=your_bucket_name
OSS_REGION=your_region
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
