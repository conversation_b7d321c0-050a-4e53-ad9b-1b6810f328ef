# 快速开始指南

## 项目概述

这是一个基于 Next.js 14 + TypeScript + Ant Design 开发的现代化报价系统，具有以下特性：

- 🔐 钉钉扫码登录（模拟实现）
- 📱 响应式设计，支持移动端
- 🎨 现代化UI界面
- 📊 产品分类展示
- ⚙️ 后台管理系统
- 🔍 产品搜索功能

## 启动项目

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 功能演示

### 登录功能
- 访问 `/login` 页面
- 点击"钉钉扫码登录"按钮
- 等待2秒模拟扫码过程
- 自动跳转到首页

### 产品展示
- 首页展示所有产品分类
- 左侧导航栏可以按分类筛选
- 支持产品搜索功能
- 点击产品卡片会显示提示信息

### 后台管理
- 访问 `/admin` 页面（需要先登录）
- 可以查看、添加、编辑、删除产品
- 支持产品分类管理
- 表格分页和搜索功能

## 技术架构

### 前端技术栈
- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS + Ant Design
- **状态管理**: React Hooks
- **图标**: Ant Design Icons

### 项目结构
```
src/
├── app/                 # Next.js App Router
│   ├── login/          # 登录页面
│   ├── admin/          # 后台管理
│   ├── layout.tsx      # 根布局
│   └── page.tsx        # 首页
├── components/         # 公共组件
│   ├── layout/        # 布局组件
│   └── product/       # 产品相关组件
├── data/              # 模拟数据
├── types/             # TypeScript类型定义
└── ...
```

## 开发说明

### 模拟数据
项目使用模拟数据进行演示，数据定义在 `src/data/mockData.ts` 中：
- 用户数据（管理员、普通用户）
- 产品分类数据
- 产品数据（21个示例产品）

### 登录状态
登录状态通过 localStorage 存储，包含用户信息：
```javascript
{
  id: '1',
  name: '管理员',
  avatar: 'https://via.placeholder.com/40',
  role: 'admin'
}
```

### 响应式设计
- 桌面端：完整的侧边栏导航
- 移动端：折叠式导航，通过汉堡菜单控制

## 下一步开发计划

1. **后端集成**
   - 实现真实的钉钉登录API
   - 连接MySQL数据库
   - 实现RESTful API

2. **功能增强**
   - 图片上传功能
   - 产品详情页面
   - 用户权限管理
   - 数据导出功能

3. **性能优化**
   - 图片懒加载
   - 虚拟滚动
   - 缓存策略

4. **部署上线**
   - Docker容器化
   - CI/CD流水线
   - 生产环境配置

## 常见问题

### Q: 如何修改产品数据？
A: 编辑 `src/data/mockData.ts` 文件中的 `mockProducts` 数组

### Q: 如何添加新的产品分类？
A: 在 `src/data/mockData.ts` 文件中的 `mockCategories` 数组添加新分类

### Q: 如何自定义主题色彩？
A: 修改 `src/app/layout.tsx` 中的 ConfigProvider theme 配置

## 技术支持

如有问题，请查看：
1. Next.js 官方文档：https://nextjs.org/docs
2. Ant Design 官方文档：https://ant.design/
3. Tailwind CSS 官方文档：https://tailwindcss.com/docs
