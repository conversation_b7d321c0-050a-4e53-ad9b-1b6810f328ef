// 用户类型
export interface User {
  id: string;
  dingtalkId?: string;
  name: string;
  avatar?: string;
  role: 'admin' | 'user';
  createdAt: Date;
}

// 产品分类类型
export interface Category {
  id: string;
  name: string;
  sortOrder: number;
  createdAt: Date;
}

// 产品类型
export interface Product {
  id: string;
  name: string;
  imageUrl: string;
  categoryId: string;
  category?: Category;
  sortOrder: number;
  createdAt: Date;
}

// 登录响应类型
export interface LoginResponse {
  user: User;
  token: string;
}

// API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 钉钉用户信息类型
export interface DingTalkUserInfo {
  openId: string;
  unionId: string;
  nick: string;
  avatarUrl?: string;
}

// 页面Props类型
export interface PageProps {
  params: { [key: string]: string };
  searchParams: { [key: string]: string | string[] | undefined };
}
