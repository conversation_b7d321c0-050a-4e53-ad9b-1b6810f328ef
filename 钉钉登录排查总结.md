# 钉钉扫码登录问题排查与解决总结

本文档记录了在集成钉钉扫码登录功能时，遇到的一系列从前端UI显示到后端逻辑处理的问题，以及相应的排查思路和解决方案。

---

## 1. 二维码显示问题

这类问题主要集中在前端UI层面，影响用户的第一交互体验。

### 1.1. 问题：二维码显示不全或变形

-   **现象:** 登录页面的钉钉二维码被裁剪，部分区域不可见，导致无法成功扫描。
-   **原因:** 在调用钉钉`DDLogin`方法时，传入的`width`和`height`参数值不当。例如，宽高不一致导致二维码不是正方形，或尺寸过小被父容器遮挡。
-   **解决方案:**
    1.  确保传递给`DDLogin`的`width`和`height`值相等，以渲染一个正方形的二维码。
    2.  给予一个合适的尺寸（例如 `280`x`280`），保证二维码清晰可见。

    ```javascript
    // src/app/login/page.tsx
    (window as any).DDLogin({
      id: qrId,
      goto: gotoUrl,
      style: 'border:none;background-color:#FFFFFF;display:block;margin:0;',
      width: '280', // 调整宽度
      height: '280' // 调整高度，与宽度保持一致
    });
    ```

### 1.2. 问题：二维码下方的提示文字被遮挡

-   **现象:** 二维码本身显示正常，但由钉钉`iframe`自带的“请使用钉钉扫描”或“二维码已失效”等提示文字被截断。
-   **原因:** 包裹二维码的父容器高度不足，无法容纳完整的`iframe`内容。
-   **解决方案:**
    1.  增加`DDLogin`方法中的`height`参数，使其大于`width`，为下方的文字留出空间（例如 `height: '310'`）。
    2.  相应地，也要增加包裹`iframe`的父`div`容器的高度，以避免新的内容溢出（例如 `h-[320px]`）。

    ```javascript
    // src/app/login/page.tsx
    
    // 增加父容器高度
    <div className="relative w-80 h-[320px] mx-auto ...">
      ...
    </div>

    // 增加 iframe 高度
    (window as any).DDLogin({
      ...
      width: '280',
      height: '310' // 高度要足以容纳文字
    });
    ```

---

## 2. 扫码后不跳转问题

这是整个流程中最核心的逻辑问题，通常涉及前后端的数据交互和通信。

### 2.1. 问题：后端接口未能正确获取用户信息

-   **现象:** 前端已正确获取`authCode`并发送给后端，但后端接口报错，未能返回用户信息，导致登录流程中断。
-   **原因:** 根据钉钉开放平台文档，通过`authCode`获取用户信息的API需要使用`POST`方法。代码中错误地使用了`GET`方法。
-   **解决方案:** 在后端API (`/api/dingtalk/getUserInfo/route.ts`) 中，将调用钉钉接口的`fetch`请求方法从`GET`改为`POST`，并将`code`参数放入请求体`body`中。

    ```typescript
    // src/app/api/dingtalk/getUserInfo/route.ts
    async function getUserInfoByCode(accessToken: string, authCode: string) {
      const response = await fetch(
        `${DINGTALK_CONFIG.USER_INFO_URL}?access_token=${accessToken}`,
        {
          method: 'POST', // 关键：使用 POST 方法
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ // 关键：将 code 放入 body
            code: authCode,
          }),
        }
      );
      // ...
    }
    ```

### 2.2. 问题：手机扫码确认后，PC端页面无反应

-   **现象:** 在手机上扫码并点击“确认登录”后，PC端的登录页面没有任何变化，停留在二维码界面。
-   **原因:** 缺少了钉钉扫码流程中至关重要的一步：**监听来自`iframe`的`postMessage`事件**。用户在手机上确认后，钉钉的登录`iframe`会向当前窗口发送一个临时的`loginTmpCode`，前端必须捕获这个码，并用它拼接成一个URL，然后手动触发页面跳转。
-   **解决方案:** 在前端初始化二维码的逻辑中，添加一个全局的`message`事件监听器。

    ```javascript
    // src/app/login/page.tsx
    const handleMessage = (event: MessageEvent) => {
      const origin = event.origin;
      // 验证消息来源
      if (origin === "https://login.dingtalk.com") { 
        const loginTmpCode = event.data;
        // 构造最终跳转链接
        const finalGotoUrl = `${gotoUrl}&loginTmpCode=${loginTmpCode}`;
        // 手动跳转
        window.location.href = finalGotoUrl;
      }
    };
    window.addEventListener('message', handleMessage, false);
    ```

---

## 3. 跳转后出现404页面

-   **现象:** 登录流程看似成功，但页面最终跳转到了一个404 Not Found页面，而不是应用首页。
-   **原因:**
    1.  **逻辑错误:** 首页组件(`src/app/page.tsx`)的`useEffect`中，检查用户登录状态后，虽然执行了`router.push('/login')`，但没有立即`return`，导致后续代码尝试解析一个不存在的用户数据(`JSON.parse(null)`)而抛出异常，中断了渲染。
    2.  **缓存问题 (更常见):** 在Next.js开发中，即便代码逻辑已经修正，旧的构建缓存或路由状态也可能导致意外的404。
-   **解决方案:**
    1.  确保在重定向前后逻辑完整，该`return`的地方要`return`。
    2.  执行一次彻底的缓存清理：
        a.  **停止**正在运行的开发服务器 (`Ctrl + C`)。
        b.  删除项目根目录下的 `.next` 和 `node_modules` 文件夹。
        c.  重新执行 `npm install` 安装所有依赖。
        d.  重新执行 `npm run dev` 启动服务。

这个“清理缓存再重装”的操作是解决Next.js中许多“灵异”问题的通用有效手段。