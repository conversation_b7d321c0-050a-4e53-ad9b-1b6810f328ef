import { Category, Product, User } from '@/types';

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    dingtalkId: 'dt001',
    name: '管理员',
    avatar: 'https://via.placeholder.com/40',
    role: 'admin',
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    dingtalkId: 'dt002',
    name: '普通用户',
    avatar: 'https://via.placeholder.com/40',
    role: 'user',
    createdAt: new Date('2024-01-02'),
  },
];

// 模拟分类数据
export const mockCategories: Category[] = [
  {
    id: '1',
    name: '无纺布袋',
    sortOrder: 1,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: '塑料袋',
    sortOrder: 2,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: '其他袋形',
    sortOrder: 3,
    createdAt: new Date('2024-01-01'),
  },
];

// 模拟产品数据
export const mockProducts: Product[] = [
  // 无纺布袋
  {
    id: '1',
    name: '热压覆膜保温立体袋',
    imageUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200&h=200&fit=crop&crop=center',
    categoryId: '1',
    sortOrder: 1,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: '热压覆膜立体袋',
    imageUrl: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=200&h=200&fit=crop&crop=center',
    categoryId: '1',
    sortOrder: 2,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: '不覆膜热压立体袋',
    imageUrl: 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=200&h=200&fit=crop&crop=center',
    categoryId: '1',
    sortOrder: 3,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '4',
    name: '有底无侧不覆膜无纺布袋',
    imageUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=200&fit=crop&crop=center',
    categoryId: '1',
    sortOrder: 4,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '5',
    name: '车缝不覆膜立体袋',
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=200&fit=crop&crop=center',
    categoryId: '1',
    sortOrder: 5,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '6',
    name: '车缝覆膜无纺布袋',
    imageUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200&h=200&fit=crop&crop=center&auto=format',
    categoryId: '1',
    sortOrder: 6,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '7',
    name: '平口不覆膜无纺布袋',
    imageUrl: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=200&h=200&fit=crop&crop=center&auto=format',
    categoryId: '1',
    sortOrder: 7,
    createdAt: new Date('2024-01-01'),
  },

  // 塑料袋
  {
    id: '8',
    name: '有侧无底手提袋',
    imageUrl: 'https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=200&h=200&fit=crop&crop=center',
    categoryId: '2',
    sortOrder: 1,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '9',
    name: '有底无侧手提袋',
    imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=200&h=200&fit=crop&crop=center',
    categoryId: '2',
    sortOrder: 2,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '10',
    name: '有侧无底磨砂袋',
    imageUrl: 'https://images.unsplash.com/photo-1582142306909-195724d33ffc?w=200&h=200&fit=crop&crop=center',
    categoryId: '2',
    sortOrder: 3,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '11',
    name: '有底无侧磨砂袋',
    imageUrl: 'https://images.unsplash.com/photo-1607083206325-cbb6c2c1756e?w=200&h=200&fit=crop&crop=center',
    categoryId: '2',
    sortOrder: 4,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '12',
    name: '平口有侧塑料袋',
    imageUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '2',
    sortOrder: 5,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '13',
    name: '平口有底塑料袋',
    imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '2',
    sortOrder: 6,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '14',
    name: '磨砂抽绳袋',
    imageUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '2',
    sortOrder: 7,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '15',
    name: '快递袋',
    imageUrl: 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '2',
    sortOrder: 8,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '16',
    name: '平口有底磨砂袋',
    imageUrl: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '2',
    sortOrder: 9,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '17',
    name: '背心袋',
    imageUrl: 'https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '2',
    sortOrder: 10,
    createdAt: new Date('2024-01-01'),
  },

  // 其他袋形
  {
    id: '18',
    name: '卡纸手提袋',
    imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '3',
    sortOrder: 1,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '19',
    name: '车缝编织袋',
    imageUrl: 'https://images.unsplash.com/photo-1582142306909-195724d33ffc?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '3',
    sortOrder: 2,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '20',
    name: '立体热压编织袋',
    imageUrl: 'https://images.unsplash.com/photo-1607083206325-cbb6c2c1756e?w=200&h=200&fit=crop&crop=center&q=80',
    categoryId: '3',
    sortOrder: 3,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: '21',
    name: 'PVC包',
    imageUrl: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=200&h=200&fit=crop&crop=center&q=80&sat=-100',
    categoryId: '3',
    sortOrder: 4,
    createdAt: new Date('2024-01-01'),
  },
];

// 获取带分类信息的产品数据
export const getProductsWithCategory = (): (Product & { category: Category })[] => {
  return mockProducts.map(product => ({
    ...product,
    category: mockCategories.find(cat => cat.id === product.categoryId)!,
  }));
};
