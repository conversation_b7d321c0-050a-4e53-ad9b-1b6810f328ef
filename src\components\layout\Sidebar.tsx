'use client';

import React from 'react';
import { Layout, Menu, Input } from 'antd';
import {
  HomeOutlined,
  ShoppingOutlined,
  SettingOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { Category } from '@/types';

const { Sider } = Layout;
const { Search } = Input;

interface SidebarProps {
  categories: Category[];
  selectedCategory?: string;
  onCategorySelect?: (categoryId: string) => void;
  onSearch?: (value: string) => void;
  collapsed?: boolean;
}

export default function Sidebar({ 
  categories, 
  selectedCategory, 
  onCategorySelect, 
  onSearch,
  collapsed 
}: SidebarProps) {
  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '秒报价首页',
      onClick: () => onCategorySelect?.(''),
    },
    {
      type: 'divider' as const,
    },
    ...categories.map(category => ({
      key: category.id,
      icon: <ShoppingOutlined />,
      label: category.name,
      onClick: () => onCategorySelect?.(category.id),
    })),
    {
      type: 'divider' as const,
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  return (
    <Sider
      width={280}
      className="bg-white shadow-sm border-r border-gray-200"
      collapsed={collapsed}
      collapsedWidth={0}
      breakpoint="lg"
      zeroWidthTriggerStyle={{ top: 64 }}
    >
      <div className="p-4 border-b border-gray-200">
        <Search
          placeholder="搜索产品"
          allowClear
          onSearch={onSearch}
          className="w-full"
          prefix={<SearchOutlined className="text-gray-400" />}
        />
      </div>

      <Menu
        mode="inline"
        selectedKeys={selectedCategory ? [selectedCategory] : ['home']}
        items={menuItems}
        className="border-none sidebar-menu"
        style={{
          height: 'calc(100vh - 192px)',
          overflowY: 'auto',
          backgroundColor: 'white'
        }}
      />
    </Sider>
  );
}
