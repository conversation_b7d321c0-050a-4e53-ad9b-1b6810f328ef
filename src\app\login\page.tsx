'use client';

import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useRouter } from 'next/navigation';
import { getDingTalkUserInfoByCode } from '@/utils/dingtalk';
import { DINGTALK_CONFIG } from '@/config/dingtalk';
import '@fortawesome/fontawesome-free/css/all.min.css';

export default function LoginPage() {
  const [activeTab, setActiveTab] = useState('scan');
  const [loginLoading, setLoginLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const authCode = urlParams.get('code');
    const error = urlParams.get('error');

    if (authCode) {
      handleDingTalkCallback(authCode);
    } else if (error) {
      message.error('钉钉登录授权失败，请重试');
      console.error('钉钉登录错误:', error);
    }
  }, []);

  const handleDingTalkCallback = async (authCode: string) => {
    setLoginLoading(true);
    try {
      const userInfo = await getDingTalkUserInfoByCode(authCode);
      if (userInfo.success) {
        message.success('钉钉登录成功！');
        router.push('/?user=' + encodeURIComponent(JSON.stringify(userInfo.user)));
      } else {
        throw new Error(userInfo.error || '登录失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '登录失败，请重试');
    } finally {
      setLoginLoading(false);
      window.history.replaceState({}, document.title, '/login');
    }
  };

  const qrContainerId = 'dingtalk_login_container';

  useEffect(() => {
    // 初始化钉钉扫码登录二维码
    const initDDLogin = () => {
      const redirectUriEnc = encodeURIComponent(DINGTALK_CONFIG.REDIRECT_URI);
      const goto = encodeURIComponent(
        `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${DINGTALK_CONFIG.APP_KEY}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${redirectUriEnc}`
      );
      const DDLogin = (window as any).DDLogin;
      if (DDLogin) {
        DDLogin({
          id: qrContainerId,
          goto,
          style: 'border:none;background-color:#FFFFFF;display:block;margin:0;',
          width: '280',
          height: '310', // 预留下方提示文字空间
        });
      }
    };

    // 监听来自钉钉 iframe 的消息，拿到 loginTmpCode 后跳转以获取最终 code
    const handleMessage = (event: MessageEvent) => {
      if (event.origin === 'https://login.dingtalk.com') {
        const loginTmpCode = event.data as string;
        const redirectUriEnc = encodeURIComponent(DINGTALK_CONFIG.REDIRECT_URI);
        const finalGoto = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${DINGTALK_CONFIG.APP_KEY}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${redirectUriEnc}&loginTmpCode=${loginTmpCode}`;
        window.location.href = finalGoto;
      }
    };

    // 动态加载钉钉登录脚本
    const scriptId = 'ddlogin_js';
    if (!document.getElementById(scriptId)) {
      const s = document.createElement('script');
      s.id = scriptId;
      s.src = 'https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js';
      s.onload = initDDLogin;
      document.body.appendChild(s);
    } else {
      initDDLogin();
    }

    window.addEventListener('message', handleMessage, false);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  return (
    <div className="font-sans relative min-h-screen flex items-center justify-center overflow-hidden" style={{ backgroundColor: '#f8fafc' }}>
      <div className="absolute top-0 -left-20 w-96 h-96 bg-yellow-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob"></div>
      <div className="absolute top-0 -right-20 w-96 h-96 bg-teal-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-2000"></div>
      <div className="absolute -bottom-20 left-1/3 w-96 h-96 bg-slate-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-4000"></div>

      <div className="relative z-10 w-full max-w-md p-4">
        <div className="bg-white rounded-2xl shadow-xl p-8">

          <div className="text-center mb-10">
            <div className="inline-flex items-center text-3xl font-bold text-slate-800">
              <i className="fas fa-bolt-lightning text-yellow-400 mr-3"></i>
              <span>秒报价系统</span>
            </div>
            <p className="text-slate-500 text-sm mt-2">欢迎回来，请登录</p>
          </div>

          <div className="flex justify-center border-b mb-8">
            <button
              onClick={() => setActiveTab('scan')}
              className={`flex-1 pb-3 text-center font-semibold border-b-2 transition-colors duration-200 ${
                activeTab === 'scan'
                  ? 'text-slate-800 border-slate-800'
                  : 'text-gray-400 border-transparent hover:text-slate-600'
              }`}
            >
              扫码登录
            </button>
            <button
              onClick={() => setActiveTab('pwd')}
              className={`flex-1 pb-3 text-center font-semibold border-b-2 transition-colors duration-200 ${
                activeTab === 'pwd'
                  ? 'text-slate-800 border-slate-800'
                  : 'text-gray-400 border-transparent hover:text-slate-600'
              }`}
            >
              账号登录
            </button>
          </div>

          {activeTab === 'scan' && (
            <div>
              {loginLoading ? (
                <div className="flex flex-col items-center justify-center h-[300px]">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-800 mb-4"></div>
                  <p className="text-slate-600">正在验证登录...</p>
                </div>
              ) : (
                <div className="w-full h-[320px] flex justify-center items-center">
                  <iframe
                    src={iframeSrc}
                    style={{ border: 'none', backgroundColor: 'white' }}
                    scrolling="no"
                    width="300px"
                    height="300px"
                    title="dingtalk-login"
                  ></iframe>
                </div>
              )}
            </div>
          )}

          {activeTab === 'pwd' && (
            <div className="pt-8 text-center text-gray-500">
              <p>暂不支持账号登录</p>
            </div>
          )}
        </div>

        <footer className="text-center mt-8">
          <p className="text-xs text-slate-500">版权所有：杭州有限公司 www.maibaodai.cn</p>
        </footer>
      </div>
    </div>
  );
}
