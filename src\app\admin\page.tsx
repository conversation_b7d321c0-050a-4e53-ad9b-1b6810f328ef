'use client';

import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useRouter } from 'next/navigation';
import { mockCategories } from '@/data/mockData';

export default function AdminPage() {
  const [user, setUser] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('home');
  const router = useRouter();

  useEffect(() => {
    // 检查用户登录状态
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }
    setUser(JSON.parse(userData));
  }, [router]);

  // 如果用户未登录，不渲染页面
  if (!user) {
    return null;
  }

  const handleCategoryClick = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleBackToHome = () => {
    router.push('/');
  };

  const handleNavigateToProducts = () => {
    router.push('/admin/products');
  };

  // 系统设置菜单项
  const settingsMenuItems = [
    {
      id: 'products',
      title: '产品管理',
      description: '管理系统中的所有产品信息，包括添加、编辑和删除产品',
      icon: 'fa-box',
      color: 'bg-blue-500',
      onClick: handleNavigateToProducts
    },
    {
      id: 'categories',
      title: '分类管理',
      description: '管理产品分类，设置分类排序和显示规则',
      icon: 'fa-tags',
      color: 'bg-green-500',
      onClick: () => message.info('分类管理功能开发中...')
    },
    {
      id: 'users',
      title: '用户管理',
      description: '管理系统用户，设置用户权限和角色',
      icon: 'fa-users',
      color: 'bg-purple-500',
      onClick: () => message.info('用户管理功能开发中...')
    },
    {
      id: 'settings',
      title: '系统配置',
      description: '系统基础配置，包括主题、语言和其他设置',
      icon: 'fa-cogs',
      color: 'bg-orange-500',
      onClick: () => message.info('系统配置功能开发中...')
    }
  ];



  return (
    <div className="relative min-h-screen font-sans overflow-hidden" style={{ backgroundColor: '#f8fafc' }}>
      {/* Background decorative shapes - 动态背景装饰 */}
      <div className="absolute top-0 -left-20 w-96 h-96 bg-yellow-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob"></div>
      <div className="absolute top-0 -right-20 w-96 h-96 bg-teal-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-2000"></div>
      <div className="absolute -bottom-20 left-1/3 w-96 h-96 bg-slate-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-4000"></div>

      <div className="flex h-screen relative z-10">
        {/* Left Sidebar Navigation - 清晰侧边栏 */}
        <aside className="w-64 bg-white border-r border-gray-200 flex-col fixed inset-y-0 left-0 hidden md:flex shadow-xl">
          <div className="h-16 flex items-center justify-center text-xl font-bold border-b border-gray-200/50 text-slate-800">
            <i className="fas fa-bolt-lightning text-yellow-400 mr-2"></i>
            <span>秒报价系统</span>
          </div>
          <nav className="flex-1 px-4 py-6 space-y-1">
            <a
              href="#"
              onClick={handleBackToHome}
              className={`flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-slate-600 hover:bg-slate-50/80 hover:text-slate-800`}
            >
              <i className="fas fa-th-large w-5 h-5 mr-3"></i>
              <span>秒报价首页</span>
            </a>
            {mockCategories.map((category) => (
              <a
                key={category.id}
                href="#"
                onClick={() => handleCategoryClick(category.id)}
                className={`flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-slate-600 hover:bg-slate-50/80 hover:text-slate-800`}
              >
                <i className={`fas ${getCategoryIcon(category.name)} w-5 h-5 mr-3`}></i>
                <span>{category.name}</span>
              </a>
            ))}
            <a
              href="#"
              className="flex items-center px-4 py-2.5 text-sm font-semibold rounded-lg bg-slate-100/80 text-slate-800 shadow-sm"
            >
              <i className="fas fa-cog w-5 h-5 mr-3"></i>
              <span>系统设置</span>
            </a>
          </nav>
          <div className="p-4 border-t border-gray-200/50">
            <button className="w-full flex items-center justify-center text-sm text-slate-500 hover:text-slate-700 transition-colors duration-200">
              <i className="fas fa-bars"></i>
            </button>
          </div>
        </aside>

        {/* Main Content Area - 系统设置目录 */}
        <main className="flex-1 md:ml-64 p-6 lg:p-8 overflow-y-auto">
          <header className="mb-8">
            <h1 className="text-3xl font-bold text-slate-800 mb-2">系统设置</h1>
            <p className="text-slate-600">管理系统的各项配置和数据</p>
          </header>

          {/* Settings Menu Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {settingsMenuItems.map((item) => (
              <div
                key={item.id}
                onClick={item.onClick}
                className="bg-white p-6 rounded-2xl shadow-xl border border-gray-200 cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 group"
              >
                <div className="flex items-start space-x-4">
                  <div className={`w-12 h-12 ${item.color} rounded-xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`fas ${item.icon} text-lg`}></i>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-slate-900">
                      {item.title}
                    </h3>
                    <p className="text-slate-600 text-sm leading-relaxed">
                      {item.description}
                    </p>
                  </div>
                </div>
                <div className="mt-4 flex justify-end">
                  <i className="fas fa-arrow-right text-slate-400 group-hover:text-slate-600 group-hover:translate-x-1 transition-all duration-300"></i>
                </div>
              </div>
            ))}
          </div>

          {/* Quick Stats Section */}
          <section className="mt-8 bg-white p-6 lg:p-8 rounded-2xl shadow-xl border border-gray-200">
            <h2 className="text-lg font-semibold text-slate-800 flex items-center mb-6">
              <i className="fas fa-chart-bar mr-3 text-yellow-400"></i>
              系统概览
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-slate-50 rounded-xl">
                <div className="text-2xl font-bold text-slate-800 mb-1">21</div>
                <div className="text-sm text-slate-600">产品总数</div>
              </div>
              <div className="text-center p-4 bg-slate-50 rounded-xl">
                <div className="text-2xl font-bold text-slate-800 mb-1">3</div>
                <div className="text-sm text-slate-600">产品分类</div>
              </div>
              <div className="text-center p-4 bg-slate-50 rounded-xl">
                <div className="text-2xl font-bold text-slate-800 mb-1">1</div>
                <div className="text-sm text-slate-600">系统用户</div>
              </div>
            </div>
          </section>
        </main>
      </div>

    </div>
  );
}

// 获取分类图标的辅助函数
function getCategoryIcon(categoryName: string): string {
  switch (categoryName) {
    case '无纺布袋':
      return 'fa-shopping-bag';
    case '塑料袋':
      return 'fa-box';
    case '其他袋形':
      return 'fa-shapes';
    default:
      return 'fa-folder';
  }
}
