import { DINGTALK_CONFIG } from '@/config/dingtalk';

export const getDingTalkUserInfoByCode = async (authCode: string) => {
  try {
    const response = await fetch(`/api/dingtalk/getUserInfo?code=${authCode}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || '获取用户信息失败');
    }
    return await response.json();
  } catch (error) {
    console.error('获取钉钉用户信息失败:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
};
