<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>秒报价系统 - 产品分类</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            background-color: #f4f7f9; /* 一个非常浅的灰色背景 */
        }
    </style>
</head>
<body class="flex h-screen font-sans text-gray-800">
    <!-- Chosen Palette: Clean & Minimal -->
    <!-- Application Structure Plan: Based on user feedback, the structure is now a direct implementation of the provided wireframe. It abandons the dashboard concept for a simple, single-purpose page. The layout features a fixed left sidebar for navigation and a main content area that contains a single, large content card. Inside this card, product categories are listed sequentially, each with a title and a grid of products. This structure is chosen for its simplicity and directness, perfectly matching the user's explicit request for a straightforward product catalog view. -->
    <!-- Visualization & Content Choices: 
        - Report Info: Product categories and their items. Goal: Organize/Navigate. Viz: Grouped visual grids within a single content block. Interaction: Hover to highlight, click to navigate. Justification: This is a 1-to-1 implementation of the user's provided prototype, prioritizing familiarity and directness over complex interactivity. Library: HTML/Tailwind.
        - All other elements (KPIs, charts, activity feeds) have been removed as per user request.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->

    <!-- Left Sidebar Navigation -->
    <aside class="w-64 bg-white border-r border-gray-200 flex-col fixed inset-y-0 left-0 hidden md:flex">
        <div class="h-16 flex items-center justify-center text-xl font-bold border-b border-gray-200 text-gray-700">
            <i class="fas fa-bolt-lightning text-yellow-500 mr-2"></i>
            <span>秒报价系统</span>
        </div>
        <nav class="flex-1 px-4 py-6 space-y-1">
            <a href="#" class="flex items-center px-4 py-2.5 text-sm font-semibold rounded-lg bg-teal-50 text-teal-700">
                <i class="fas fa-th-large w-5 h-5 mr-3"></i>
                <span>秒报价首页</span>
            </a>
            <a href="#" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200">
                <i class="fas fa-shopping-bag w-5 h-5 mr-3"></i>
                <span>无纺布袋</span>
            </a>
            <a href="#" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200">
                <i class="fas fa-box w-5 h-5 mr-3"></i>
                <span>塑料袋</span>
            </a>
            <a href="#" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200">
                <i class="fas fa-shapes w-5 h-5 mr-3"></i>
                <span>其他袋形</span>
            </a>
            <a href="#" class="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-gray-600 hover:bg-gray-100 transition-colors duration-200">
                <i class="fas fa-cog w-5 h-5 mr-3"></i>
                <span>系统设置</span>
            </a>
        </nav>
        <div class="p-4 border-t border-gray-200">
             <button class="w-full flex items-center justify-center text-sm text-gray-500 hover:text-gray-700">
                <i class="fas fa-bars"></i>
             </button>
        </div>
    </aside>

    <!-- Main Content Area -->
    <main class="flex-1 md:ml-64 p-6 lg:p-8 overflow-y-auto">
        <header class="flex justify-between items-center mb-6">
            <div class="relative w-full max-w-xs">
                <input type="text" placeholder="搜索产品..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
                <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
            </div>
            <div class="flex items-center space-x-4 text-sm text-gray-600">
                <a href="#" class="hover:text-teal-600"><span>分享</span></a>
                <a href="#" class="hover:text-teal-600"><span>更多操作</span></a>
                <a href="#" class="hover:text-teal-600"><i class="fas fa-sync-alt"></i></a>
                <a href="#" class="hover:text-teal-600"><i class="fas fa-question-circle"></i></a>
            </div>
        </header>

        <!-- Product Categories Section -->
        <section id="products" class="bg-white p-6 lg:p-8 rounded-lg shadow-sm">
            
            <!-- 无纺布袋 Section -->
            <div class="mb-10">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center"><i class="fas fa-shopping-bag mr-3 text-teal-600"></i>无纺布袋</h2>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-x-6 gap-y-8 mt-5">
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="热压覆膜立体袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">热压覆膜立体袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="热压覆膜立体袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">热压覆膜立体袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="不覆膜立体袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">不覆膜立体袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="有底无侧不覆膜" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">有底无侧不覆膜</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="车缝不覆膜立体袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">车缝不覆膜立体袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="平口不覆膜无纺布袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">平口不覆膜无纺布袋</p></a>
                </div>
            </div>

            <!-- 塑料袋 Section -->
            <div class="mb-10">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center"><i class="fas fa-box mr-3 text-teal-600"></i>塑料袋</h2>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-x-6 gap-y-8 mt-5">
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="有底无侧手提袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">有底无侧手提袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="有底无侧手提袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">有底无侧手提袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="有底无侧手提袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">有底无侧手提袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="有底无侧手提袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">有底无侧手提袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="平口有底塑料袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">平口有底塑料袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="平口有底塑料袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">平口有底塑料袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="平口有底塑料袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">平口有底塑料袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="平口有底塑料袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">平口有底塑料袋</p></a>
                </div>
            </div>

            <!-- 其他袋形 Section -->
            <div>
                <h2 class="text-lg font-semibold text-gray-800 flex items-center"><i class="fas fa-shapes mr-3 text-teal-600"></i>其他袋形</h2>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-x-6 gap-y-8 mt-5">
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="牛津布袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">牛津布袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="帆布袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">帆布袋</p></a>
                    <a href="#" class="group"><div class="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden"><img src="https://placehold.co/300x300/f1f5f9/64748b?text=产品图" alt="PVC包装袋" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"></div><p class="mt-3 text-sm text-center font-medium text-gray-700 group-hover:text-teal-600">PVC包装袋</p></a>
                </div>
            </div>

            <footer class="mt-12 pt-6 border-t border-gray-200 text-center">
                <p class="text-xs text-gray-500">版权所有：杭州有限公司 www.maibaodai.cn</p>
            </footer>
        </section>
    </main>

    <script>
        // No complex JS needed for this layout, but keeping the script tag for potential future enhancements.
        document.addEventListener('DOMContentLoaded', function () {
            // Example: Add active state to sidebar navigation on click
            const navLinks = document.querySelectorAll('aside nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // In a real SPA, you would prevent default and load content.
                    // Here, we just style the active link.
                    navLinks.forEach(l => {
                        l.classList.remove('bg-teal-50', 'text-teal-700');
                        l.classList.add('text-gray-600');
                    });
                    this.classList.add('bg-teal-50', 'text-teal-700');
                    this.classList.remove('text-gray-600');
                });
            });
        });
    </script>
</body>
</html>
