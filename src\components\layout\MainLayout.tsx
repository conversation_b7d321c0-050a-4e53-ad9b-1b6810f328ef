'use client';

import React, { useState, useEffect } from 'react';
import { Layout } from 'antd';
import { useRouter } from 'next/navigation';
import Header from './Header';
import Sidebar from './Sidebar';
import { Category } from '@/types';

const { Content } = Layout;

interface MainLayoutProps {
  children: React.ReactNode;
  categories: Category[];
  selectedCategory?: string;
  onCategorySelect?: (categoryId: string) => void;
  onSearch?: (value: string) => void;
}

export default function MainLayout({ 
  children, 
  categories, 
  selectedCategory, 
  onCategorySelect, 
  onSearch 
}: MainLayoutProps) {
  const [collapsed, setCollapsed] = useState(false);
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // 检查用户登录状态
    const userData = localStorage.getItem('user');
    if (!userData) {
      router.push('/login');
      return;
    }
    setUser(JSON.parse(userData));
  }, [router]);

  const handleMenuToggle = () => {
    setCollapsed(!collapsed);
  };

  // 如果用户未登录，不渲染布局
  if (!user) {
    return null;
  }

  return (
    <Layout className="min-h-screen">
      <Header onMenuToggle={handleMenuToggle} collapsed={collapsed} />
      <Layout className="pt-16">
        <Sidebar
          categories={categories}
          selectedCategory={selectedCategory}
          onCategorySelect={onCategorySelect}
          onSearch={onSearch}
          collapsed={collapsed}
        />
        <Layout>
          <Content className="bg-gray-50 p-6 overflow-auto min-h-screen ml-280">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
}
