# 钉钉扫码登录配置说明

## 📋 前期准备

### 1. 创建钉钉应用
1. 访问 [钉钉开放平台](https://open.dingtalk.com/)
2. 登录并进入开发者后台
3. 创建一个新的应用（选择"扫码登录第三方网站"类型）
4. 获取应用的 `AppKey` 和 `AppSecret`

### 2. 配置回调域名
在钉钉开放平台的应用详情页面：
1. 找到"登录与分享"设置
2. 配置回调域名：
   - 开发环境：`http://localhost:3000`
   - 生产环境：`https://yourdomain.com`

## 🔧 项目配置

### 1. 环境变量配置
复制 `.env.local.example` 为 `.env.local`，并填入真实配置：

```bash
# 钉钉应用的 AppKey
NEXT_PUBLIC_DINGTALK_APP_KEY=your_actual_app_key

# 钉钉应用的 AppSecret  
DINGTALK_APP_SECRET=your_actual_app_secret

# 回调地址
NEXT_PUBLIC_DINGTALK_REDIRECT_URI=http://localhost:3000/login
```

### 2. 重启开发服务器
配置环境变量后，需要重启开发服务器：
```bash
npm run dev
```

## 🚀 功能特点

### 1. 真实钉钉扫码登录
- 集成钉钉官方 JavaScript SDK
- 支持真实的二维码扫码登录
- 自动处理授权回调

### 2. 用户信息获取
- 自动获取钉钉用户信息
- 包含用户昵称、openid、unionid 等
- 安全的服务端API处理

### 3. 登录状态管理
- 自动存储用户登录状态
- 支持登录成功后跳转
- 错误处理和用户提示

## 📱 使用流程

### 用户登录流程
1. 用户访问登录页面
2. 选择"扫码登录"标签
3. 钉钉二维码自动生成
4. 用户使用钉钉客户端扫码
5. 在钉钉客户端确认登录
6. 自动跳转回网站并完成登录

### 技术实现流程
1. 前端加载钉钉 JavaScript SDK
2. 调用 `DDLogin` 生成二维码
3. 监听扫码事件获取临时码
4. 构造授权URL并跳转
5. 钉钉回调返回授权码
6. 后端API处理授权码获取用户信息
7. 返回用户信息并完成登录

## 🔒 安全说明

### 1. AppSecret 保护
- `AppSecret` 仅在服务端使用
- 不会暴露在前端代码中
- 通过环境变量安全管理

### 2. 授权码处理
- 授权码仅在服务端处理
- 一次性使用，防止重放攻击
- 安全的用户信息获取

### 3. 回调域名验证
- 钉钉平台验证回调域名
- 防止恶意重定向攻击
- 确保登录安全性

## 🛠️ 开发调试

### 1. 常见问题
- **二维码不显示**：检查 AppKey 配置和网络连接
- **扫码后无反应**：检查回调域名配置
- **获取用户信息失败**：检查 AppSecret 配置

### 2. 调试方法
- 查看浏览器控制台日志
- 检查网络请求状态
- 验证环境变量配置

### 3. 测试建议
- 使用真实的钉钉账号测试
- 测试不同网络环境
- 验证错误处理逻辑

## 📚 相关文档

- [钉钉开放平台文档](https://open.dingtalk.com/document/)
- [扫码登录第三方网站](https://open.dingtalk.com/document/orgapp-server/scan-qr-code-to-log-on-to-third-party-websites)
- [获取用户信息API](https://open.dingtalk.com/document/orgapp-server/obtain-the-user-information-based-on-the-sns-temporary-authorization)

## 🎯 注意事项

1. **生产环境部署**：确保配置正确的生产环境回调域名
2. **HTTPS要求**：生产环境建议使用HTTPS协议
3. **用户隐私**：遵守相关隐私保护法规
4. **错误处理**：完善的错误提示和处理机制
