'use client';

import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useRouter, useSearchParams } from 'next/navigation';
import { mockCategories, mockProducts } from '@/data/mockData';
import { Product } from '@/types';
import Image from 'next/image';

export default function Home() {
  const [user, setUser] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('home');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    let userData = null;
    const userQueryParam = searchParams.get('user');

    if (userQueryParam) {
      try {
        // 优先从URL参数获取用户信息
        userData = JSON.parse(decodeURIComponent(userQueryParam));
        // 将用户信息存入localStorage，方便后续页面刷新时使用
        localStorage.setItem('user', JSON.stringify(userData));
        // 清理URL中的用户信息，使其更美观
        window.history.replaceState({}, document.title, '/');
      } catch (error) {
        console.error('解析用户信息失败:', error);
        router.push('/login');
        return;
      }
    } else {
      // 如果URL中没有，则尝试从localStorage获取
      const storedUserData = localStorage.getItem('user');
      if (storedUserData) {
        userData = JSON.parse(storedUserData);
      }
    }

    if (userData) {
      setUser(userData);
    } else {
      // 如果两种方式都获取不到，则跳转到登录页
      router.push('/login');
    }
  }, [router, searchParams]);

  const handleProductClick = (product: Product) => {
    message.info(`点击了产品：${product.name}`);
  };

  const handleCategoryClick = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      message.info(`搜索：${searchQuery}`);
    }
  };

  // 如果用户未登录，不渲染页面
  if (!user) {
    return null;
  }

  return (
    <div className="relative min-h-screen font-sans overflow-hidden" style={{ backgroundColor: '#f8fafc' }}>
      {/* Background decorative shapes - 动态背景装饰 */}
      <div className="absolute top-0 -left-20 w-96 h-96 bg-yellow-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob"></div>
      <div className="absolute top-0 -right-20 w-96 h-96 bg-teal-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-2000"></div>
      <div className="absolute -bottom-20 left-1/3 w-96 h-96 bg-slate-200 rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-blob animation-delay-4000"></div>

      <div className="flex h-screen relative z-10">
        {/* Left Sidebar Navigation - 清晰侧边栏 */}
        <aside className="w-64 bg-white border-r border-gray-200 flex-col fixed inset-y-0 left-0 hidden md:flex shadow-xl">
          <div className="h-16 flex items-center justify-center text-xl font-bold border-b border-gray-200/50 text-slate-800">
            <i className="fas fa-bolt-lightning text-yellow-400 mr-2"></i>
            <span>秒报价系统</span>
          </div>
          <nav className="flex-1 px-4 py-6 space-y-1">
            <a
              href="#"
              onClick={() => handleCategoryClick('home')}
              className={`flex items-center px-4 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 ${
                selectedCategory === 'home'
                  ? 'bg-slate-100/80 text-slate-800 shadow-sm'
                  : 'text-slate-600 hover:bg-slate-50/80 hover:text-slate-800'
              }`}
            >
              <i className="fas fa-th-large w-5 h-5 mr-3"></i>
              <span>秒报价首页</span>
            </a>
            {mockCategories.map((category) => (
              <a
                key={category.id}
                href="#"
                onClick={() => handleCategoryClick(category.id)}
                className={`flex items-center px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-slate-100/80 text-slate-800 shadow-sm'
                    : 'text-slate-600 hover:bg-slate-50/80 hover:text-slate-800'
                }`}
              >
                <i className={`fas ${getCategoryIcon(category.name)} w-5 h-5 mr-3`}></i>
                <span>{category.name}</span>
              </a>
            ))}
            <a
              href="#"
              onClick={() => router.push('/admin')}
              className="flex items-center px-4 py-2.5 text-sm font-medium rounded-lg text-slate-600 hover:bg-slate-50/80 hover:text-slate-800 transition-all duration-200"
            >
              <i className="fas fa-cog w-5 h-5 mr-3"></i>
              <span>系统设置</span>
            </a>
          </nav>
          <div className="p-4 border-t border-gray-200/50">
            <button className="w-full flex items-center justify-center text-sm text-slate-500 hover:text-slate-700 transition-colors duration-200">
              <i className="fas fa-bars"></i>
            </button>
          </div>
        </aside>

        {/* Main Content Area - 玻璃拟态主内容区 */}
        <main className="flex-1 md:ml-64 p-6 lg:p-8 overflow-y-auto">
          <header className="flex justify-between items-center mb-6">
            <div className="relative w-full max-w-xs">
              <form onSubmit={handleSearch}>
                <input
                  type="text"
                  placeholder="搜索产品..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500/50 shadow-sm transition-all duration-200"
                />
                <i className="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-slate-400"></i>
              </form>
            </div>
            <div className="flex items-center space-x-4 text-sm text-slate-600">
              <a href="#" className="hover:text-slate-800 transition-colors duration-200"><span>分享</span></a>
              <a href="#" className="hover:text-slate-800 transition-colors duration-200"><span>更多操作</span></a>
              <a href="#" className="hover:text-slate-800 transition-colors duration-200"><i className="fas fa-sync-alt"></i></a>
              <a href="#" className="hover:text-slate-800 transition-colors duration-200"><i className="fas fa-question-circle"></i></a>
            </div>
          </header>

          {/* Product Categories Section - 清晰产品展示区 */}
          <section id="products" className="bg-white p-6 lg:p-8 rounded-2xl shadow-xl border border-gray-200">
            {mockCategories.map((category) => {
              const categoryProducts = mockProducts.filter(product => product.categoryId === category.id);

              return (
                <div key={category.id} className="mb-10 last:mb-0">
                  <h2 className="text-lg font-semibold text-slate-800 flex items-center mb-5">
                    <i className={`fas ${getCategoryIcon(category.name)} mr-3 text-yellow-400`}></i>
                    {category.name}
                  </h2>
                  <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-9 2xl:grid-cols-10 gap-x-3 gap-y-4">
                    {categoryProducts.map((product) => (
                      <a
                        key={product.id}
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handleProductClick(product);
                        }}
                        className="group cursor-pointer"
                      >
                        <div className="aspect-square bg-slate-100 rounded-xl flex items-center justify-center overflow-hidden mb-3 shadow-sm border border-slate-200 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105">
                          <Image
                            src={product.imageUrl}
                            alt={product.name}
                            width={200}
                            height={200}
                            className="w-full h-full object-cover transition-transform duration-300"
                          />
                        </div>
                        <p className="text-xs text-center font-medium text-slate-700 group-hover:text-slate-900 leading-tight px-0.5 transition-colors duration-200">
                          {product.name}
                        </p>
                      </a>
                    ))}
                  </div>
                </div>
              );
            })}

            <footer className="mt-12 pt-6 border-t border-slate-200/50 text-center">
              <p className="text-xs text-slate-500">版权所有：杭州有限公司 www.maibaodai.cn</p>
            </footer>
          </section>
        </main>
      </div>
    </div>
  );
}

// 获取分类图标的辅助函数
function getCategoryIcon(categoryName: string): string {
  switch (categoryName) {
    case '无纺布袋':
      return 'fa-shopping-bag';
    case '塑料袋':
      return 'fa-box';
    case '其他袋形':
      return 'fa-shapes';
    default:
      return 'fa-folder';
  }
}