'use client';

import React from 'react';
import { Card } from 'antd';
import { Product } from '@/types';
import Image from 'next/image';

interface ProductCardProps {
  product: Product;
  onClick?: (product: Product) => void;
}

const { Meta } = Card;

export default function ProductCard({ product, onClick }: ProductCardProps) {
  return (
    <Card
      hoverable
      className="h-full transition-all duration-300 hover:shadow-xl hover:-translate-y-1 cursor-pointer border border-gray-200 hover:border-blue-300"
      cover={
        <div className="relative h-48 overflow-hidden bg-gray-50">
          <Image
            src={product.imageUrl}
            alt={product.name}
            fill
            className="object-cover transition-transform duration-300 hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      }
      onClick={() => onClick?.(product)}
    >
      <Meta
        title={
          <div className="text-center py-2">
            <h3 className="text-sm font-medium text-gray-800 line-clamp-2 leading-5">
              {product.name}
            </h3>
            <div className="mt-2 text-xs text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity">
              点击查看详情
            </div>
          </div>
        }
      />
    </Card>
  );
}
