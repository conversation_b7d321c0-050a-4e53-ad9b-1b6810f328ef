import { NextRequest, NextResponse } from 'next/server';
import { DINGTALK_CONFIG } from '@/config/dingtalk';

async function getUserAccessToken(authCode: string) {
  try {
    const response = await fetch(DINGTALK_CONFIG.ACCESS_TOKEN_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientId: DINGTALK_CONFIG.APP_KEY,
        clientSecret: DINGTALK_CONFIG.APP_SECRET,
        code: authCode,
        grantType: 'authorization_code',
      }),
    });
    const result = await response.json();
    if (result.accessToken) {
      return result.accessToken;
    } else {
      throw new Error(`获取用户访问令牌失败: ${JSON.stringify(result)}`);
    }
  } catch (error) {
    console.error('获取钉钉用户访问令牌失败:', error);
    throw error;
  }
}

async function getUserInfo(accessToken: string) {
  try {
    const response = await fetch(DINGTALK_CONFIG.USER_INFO_URL, {
      method: 'GET',
      headers: { 'x-acs-dingtalk-access-token': accessToken },
    });
    const result = await response.json();
    if (result.unionId) {
      return result;
    } else {
      throw new Error(`获取用户信息失败: ${JSON.stringify(result)}`);
    }
  } catch (error) {
    console.error('获取钉钉用户信息失败:', error);
    throw error;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const authCode = searchParams.get('code');

    if (!authCode) {
      return NextResponse.json({ success: false, error: '授权码不能为空' }, { status: 400 });
    }

    const accessToken = await getUserAccessToken(authCode);
    const userInfo = await getUserInfo(accessToken);

    const user = {
      id: userInfo.unionId,
      name: userInfo.nick || '钉钉用户',
      avatar: userInfo.avatarUrl || 'https://via.placeholder.com/40',
      role: 'user', 
      dingtalkId: userInfo.openId,
      unionid: userInfo.unionId,
    };

    return NextResponse.json({ success: true, user });

  } catch (error) {
    console.error('钉钉登录处理失败:', error);
    return NextResponse.json({ success: false, error: error instanceof Error ? error.message : '登录失败，请重试' }, { status: 500 });
  }
}
