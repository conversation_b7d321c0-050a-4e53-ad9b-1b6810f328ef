@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 玻璃拟态动画效果 */
@keyframes blob-animation {
  0% {
    transform: scale(1) translate(0, 0);
  }
  33% {
    transform: scale(1.1) translate(20px, -30px);
  }
  66% {
    transform: scale(0.9) translate(-20px, 20px);
  }
  100% {
    transform: scale(1) translate(0, 0);
  }
}

.animate-blob {
  animation: blob-animation 15s infinite ease-in-out;
}

.animation-delay-2000 {
  animation-delay: -2s;
}

.animation-delay-4000 {
  animation-delay: -4s;
}

/* 自定义样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ant Design 样式覆盖 */
.ant-layout-sider-zero-width-trigger {
  top: 64px !important;
}

/* 强制文字颜色 */
.ant-typography {
  color: inherit !important;
}

.ant-layout-header .ant-typography {
  color: #374151 !important; /* text-gray-700 */
}

/* 侧边栏菜单样式 */
.sidebar-menu .ant-menu-item {
  color: #374151 !important; /* 深灰色文字 */
}

.sidebar-menu .ant-menu-item:hover {
  color: #1890ff !important; /* 悬停时蓝色 */
  background-color: #f0f9ff !important; /* 悬停背景 */
}

.sidebar-menu .ant-menu-item-selected {
  color: #1890ff !important; /* 选中时蓝色 */
  background-color: #e6f7ff !important; /* 选中背景 */
}

.sidebar-menu .ant-menu-title-content {
  color: inherit !important;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .ml-280 {
    margin-left: 0 !important;
  }

  .ant-layout-sider {
    position: fixed !important;
    z-index: 1000;
    height: calc(100vh - 64px) !important;
    top: 64px !important;
    left: 0 !important;
  }
}

@media (min-width: 1024px) {
  .ml-280 {
    margin-left: 280px !important;
  }
}

/* 新首页样式 */
.aspect-square {
  aspect-ratio: 1 / 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 产品卡片悬停效果 */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* 输入框聚焦效果 - 更新为 slate 色系 */
input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(71, 85, 105, 0.2);
}

/* 导航链接悬停效果 - 清晰无模糊 */
nav a:hover {
  transform: translateX(2px);
}

/* 清晰卡片效果 - 无模糊 */
.glass-card {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(148, 163, 184, 0.3);
}

/* 增强的悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 字体平滑 */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 响应式网格优化 */
@media (min-width: 1536px) {
  .grid-cols-2xl-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
}

@media (min-width: 1920px) {
  .grid-cols-3xl-9 {
    grid-template-columns: repeat(9, minmax(0, 1fr));
  }
}

@media (min-width: 2560px) {
  .grid-cols-4xl-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

/* 产品卡片容器优化 - 更紧凑的布局 */
.product-grid {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(auto-fill, minmax(105px, 1fr));
}

/* 管理页面表格样式 */
.admin-table .ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.admin-table .ant-table-thead > tr > th {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  color: #334155;
}

.admin-table .ant-table-tbody > tr:hover > td {
  background-color: #f8fafc;
}

.admin-table .ant-pagination {
  margin-top: 24px;
}

/* 管理页面模态框样式 */
.admin-modal .ant-modal-content {
  border-radius: 16px;
}

.admin-modal .ant-modal-header {
  border-radius: 16px 16px 0 0;
  background-color: #f8fafc;
}

.admin-modal .ant-modal-title {
  color: #334155;
  font-weight: 600;
}

@media (min-width: 640px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 0.875rem;
  }
}

@media (min-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 1rem;
  }
}

@media (min-width: 1280px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1.125rem;
  }
}

@media (min-width: 1536px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1.125rem;
  }
}
