[{"type": "stdio", "name": "寸止", "command": "寸止", "arguments": "", "useShellInterpolation": true, "id": "1b24cb25-a784-47dc-bf1b-c351a2765369", "tools": [{"definition": {"name": "zhi___", "description": "智能代码审查交互工具，支持预定义选项、自由文本输入和图片上传", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"is_markdown\":{\"description\":\"消息是否为Markdown格式，默认为true\",\"type\":\"boolean\"},\"message\":{\"description\":\"要显示给用户的消息\",\"type\":\"string\"},\"predefined_options\":{\"description\":\"预定义的选项列表（可选）\",\"items\":{\"type\":\"string\"},\"type\":\"array\"}},\"required\":[\"message\"]}", "tool_safety": 0, "original_mcp_server_name": "寸止", "mcp_server_name": "__", "mcp_tool_name": "zhi"}, "identifier": {"hostName": "mcpHost", "toolId": "zhi___"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "ji___", "description": "全局记忆管理工具，用于存储和管理重要的开发规范、用户偏好和最佳实践", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"action\":{\"description\":\"操作类型：记忆(添加记忆), 回忆(获取项目信息)\",\"type\":\"string\"},\"category\":{\"description\":\"记忆分类：rule(规范规则), preference(用户偏好), pattern(最佳实践), context(项目上下文)\",\"type\":\"string\"},\"content\":{\"description\":\"记忆内容（记忆操作时必需）\",\"type\":\"string\"},\"project_path\":{\"description\":\"项目路径（必需）\",\"type\":\"string\"}},\"required\":[\"action\",\"project_path\"]}", "tool_safety": 0, "original_mcp_server_name": "寸止", "mcp_server_name": "__", "mcp_tool_name": "ji"}, "identifier": {"hostName": "mcpHost", "toolId": "ji___"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "stdio", "name": "mysql-1", "command": "cmd /c npx -y mysql-mcp-server", "arguments": "", "useShellInterpolation": true, "env": {"MYSQL_HOST": "rm-bp19i4ff32kmp4q65ro.mysql.rds.aliyuncs.com", "MYSQL_PORT": "3306", "MYSQL_USER": "wb", "MYSQL_PASSWORD": "hhysbyj1234!", "MYSQL_DATABASE": "lirun"}, "id": "dc353ab9-edc2-41b8-b525-961f7b1e8fb2", "tools": [{"definition": {"name": "list_databases_mysql-1", "description": "List all accessible databases on the MySQL server", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "mysql-1", "mcp_server_name": "mysql-1", "mcp_tool_name": "list_databases"}, "identifier": {"hostName": "mcpHost", "toolId": "list_databases_mysql-1"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "list_tables_mysql-1", "description": "List all tables in a specified database", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"database\":{\"type\":\"string\",\"description\":\"Database name (optional, uses default if not specified)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "mysql-1", "mcp_server_name": "mysql-1", "mcp_tool_name": "list_tables"}, "identifier": {"hostName": "mcpHost", "toolId": "list_tables_mysql-1"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "describe_table_mysql-1", "description": "Show the schema for a specific table", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"database\":{\"type\":\"string\",\"description\":\"Database name (optional, uses default if not specified)\"},\"table\":{\"type\":\"string\",\"description\":\"Table name\"}},\"required\":[\"table\"]}", "tool_safety": 0, "original_mcp_server_name": "mysql-1", "mcp_server_name": "mysql-1", "mcp_tool_name": "describe_table"}, "identifier": {"hostName": "mcpHost", "toolId": "describe_table_mysql-1"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "execute_query_mysql-1", "description": "Execute a read-only SQL query", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\",\"description\":\"SQL query (only SELECT, SHOW, DESCRIBE, and EXPLAIN statements are allowed)\"},\"database\":{\"type\":\"string\",\"description\":\"Database name (optional, uses default if not specified)\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "mysql-1", "mcp_server_name": "mysql-1", "mcp_tool_name": "execute_query"}, "identifier": {"hostName": "mcpHost", "toolId": "execute_query_mysql-1"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "stdio", "name": "钉钉 API-API 文档", "command": "cmd /c npx -y apifox-mcp-server@latest --site-id=467052", "arguments": "", "useShellInterpolation": true, "id": "63b88e47-215e-430a-8882-730497d2db9c", "disabled": true, "tools": []}, {"type": "stdio", "name": "mcp-feedback-enhanced", "command": "uvx mcp-feedback-enhanced@latest", "arguments": "", "useShellInterpolation": true, "id": "88e59d94-37bc-457a-be3b-196693271fc1", "tools": [], "disabled": true}, {"type": "stdio", "name": "chrome-mcp-stdio", "command": "node C:\\nvm4w\\nodejs\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js", "arguments": "", "useShellInterpolation": true, "id": "6f9b714c-a895-4957-9f4d-4ca2cde6dae6", "tools": [{"definition": {"name": "get_windows_and_tabs_chrome-mcp-stdio", "description": "Get all currently open browser windows and tabs", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "get_windows_and_tabs"}, "identifier": {"hostName": "mcpHost", "toolId": "get_windows_and_tabs_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_navigate_chrome-mcp-stdio", "description": "Navigate to a URL or refresh the current tab", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to navigate to the website specified\"},\"newWindow\":{\"type\":\"boolean\",\"description\":\"Create a new window to navigate to the URL or not. Defaults to false\"},\"width\":{\"type\":\"number\",\"description\":\"Viewport width in pixels (default: 1280)\"},\"height\":{\"type\":\"number\",\"description\":\"Viewport height in pixels (default: 720)\"},\"refresh\":{\"type\":\"boolean\",\"description\":\"Refresh the current active tab instead of navigating to a URL. When true, the url parameter is ignored. Defaults to false\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_navigate"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_navigate_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_screenshot_chrome-mcp-stdio", "description": "Take a screenshot of the current page or a specific element(if you want to see the page, recommend to use chrome_get_web_content first)", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\",\"description\":\"Name for the screenshot, if saving as PNG\"},\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for element to screenshot\"},\"width\":{\"type\":\"number\",\"description\":\"Width in pixels (default: 800)\"},\"height\":{\"type\":\"number\",\"description\":\"Height in pixels (default: 600)\"},\"storeBase64\":{\"type\":\"boolean\",\"description\":\"return screenshot in base64 format (default: false) if you want to see the page, recommend set this to be true\"},\"fullPage\":{\"type\":\"boolean\",\"description\":\"Store screenshot of the entire page (default: true)\"},\"savePng\":{\"type\":\"boolean\",\"description\":\"Save screenshot as PNG file (default: true)，if you want to see the page, recommend set this to be false, and set storeBase64 to be true\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_screenshot"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_screenshot_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_close_tabs_chrome-mcp-stdio", "description": "Close one or more browser tabs", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"tabIds\":{\"type\":\"array\",\"items\":{\"type\":\"number\"},\"description\":\"Array of tab IDs to close. If not provided, will close the active tab.\"},\"url\":{\"type\":\"string\",\"description\":\"Close tabs matching this URL. Can be used instead of tabIds.\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_close_tabs"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_close_tabs_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_go_back_or_forward_chrome-mcp-stdio", "description": "Navigate back or forward in browser history", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"isForward\":{\"type\":\"boolean\",\"description\":\"Go forward in history if true, go back if false (default: false)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_go_back_or_forward"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_go_back_or_forward_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_get_web_content_chrome-mcp-stdio", "description": "Fetch content from a web page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to fetch content from. If not provided, uses the current active tab\"},\"htmlContent\":{\"type\":\"boolean\",\"description\":\"Get the visible HTML content of the page. If true, textContent will be ignored (default: false)\"},\"textContent\":{\"type\":\"boolean\",\"description\":\"Get the visible text content of the page with metadata. Ignored if htmlContent is true (default: true)\"},\"selector\":{\"type\":\"string\",\"description\":\"CSS selector to get content from a specific element. If provided, only content from this element will be returned\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_get_web_content"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_get_web_content_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_click_element_chrome-mcp-stdio", "description": "Click on an element in the current page or at specific coordinates", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for the element to click. Either selector or coordinates must be provided. if coordinates are not provided, the selector must be provided.\"},\"coordinates\":{\"type\":\"object\",\"description\":\"Coordinates to click at (relative to viewport). If provided, takes precedence over selector.\",\"properties\":{\"x\":{\"type\":\"number\",\"description\":\"X coordinate relative to the viewport\"},\"y\":{\"type\":\"number\",\"description\":\"Y coordinate relative to the viewport\"}},\"required\":[\"x\",\"y\"]},\"waitForNavigation\":{\"type\":\"boolean\",\"description\":\"Wait for page navigation to complete after click (default: false)\"},\"timeout\":{\"type\":\"number\",\"description\":\"Timeout in milliseconds for waiting for the element or navigation (default: 5000)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_click_element"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_click_element_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_fill_or_select_chrome-mcp-stdio", "description": "Fill a form element or select an option with the specified value", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for the input element to fill or select\"},\"value\":{\"type\":\"string\",\"description\":\"Value to fill or select into the element\"}},\"required\":[\"selector\",\"value\"]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_fill_or_select"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_fill_or_select_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_get_interactive_elements_chrome-mcp-stdio", "description": "Get interactive elements from the current page", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"textQuery\":{\"type\":\"string\",\"description\":\"Text to search for within interactive elements (fuzzy search)\"},\"selector\":{\"type\":\"string\",\"description\":\"CSS selector to filter interactive elements. Takes precedence over textQuery if both are provided.\"},\"includeCoordinates\":{\"type\":\"boolean\",\"description\":\"Include element coordinates in the response (default: true)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_get_interactive_elements"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_get_interactive_elements_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_network_request_chrome-mcp-stdio", "description": "Send a network request from the browser with cookies and other browser context", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to send the request to\"},\"method\":{\"type\":\"string\",\"description\":\"HTTP method to use (default: GET)\"},\"headers\":{\"type\":\"object\",\"description\":\"Headers to include in the request\"},\"body\":{\"type\":\"string\",\"description\":\"Body of the request (for POST, PUT, etc.)\"},\"timeout\":{\"type\":\"number\",\"description\":\"Timeout in milliseconds (default: 30000)\"}},\"required\":[\"url\"]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_network_request"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_network_request_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_network_debugger_start_chrome-mcp-stdio", "description": "Start capturing network requests from a web page using Chrome Debugger API（with responseBody）", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to capture network requests from. If not provided, uses the current active tab\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_network_debugger_start"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_network_debugger_start_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_network_debugger_stop_chrome-mcp-stdio", "description": "Stop capturing network requests using Chrome Debugger API and return the captured data", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_network_debugger_stop"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_network_debugger_stop_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_network_capture_start_chrome-mcp-stdio", "description": "Start capturing network requests from a web page using Chrome webRequest API(without responseBody)", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to capture network requests from. If not provided, uses the current active tab\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_network_capture_start"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_network_capture_start_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_network_capture_stop_chrome-mcp-stdio", "description": "Stop capturing network requests using webRequest API and return the captured data", "input_schema_json": "{\"type\":\"object\",\"properties\":{},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_network_capture_stop"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_network_capture_stop_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_keyboard_chrome-mcp-stdio", "description": "Simulate keyboard events in the browser", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"keys\":{\"type\":\"string\",\"description\":\"Keys to simulate (e.g., \\\"Enter\\\", \\\"Ctrl+C\\\", \\\"A,B,C\\\" for sequence)\"},\"selector\":{\"type\":\"string\",\"description\":\"CSS selector for the element to send keyboard events to (optional, defaults to active element)\"},\"delay\":{\"type\":\"number\",\"description\":\"Delay between key sequences in milliseconds (optional, default: 0)\"}},\"required\":[\"keys\"]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_keyboard"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_keyboard_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_history_chrome-mcp-stdio", "description": "Retrieve and search browsing history from Chrome", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"text\":{\"type\":\"string\",\"description\":\"Text to search for in history URLs and titles. Leave empty to retrieve all history entries within the time range.\"},\"startTime\":{\"type\":\"string\",\"description\":\"Start time as a date string. Supports ISO format (e.g., \\\"2023-10-01\\\", \\\"2023-10-01T14:30:00\\\"), relative times (e.g., \\\"1 day ago\\\", \\\"2 weeks ago\\\", \\\"3 months ago\\\", \\\"1 year ago\\\"), and special keywords (\\\"now\\\", \\\"today\\\", \\\"yesterday\\\"). Default: 24 hours ago\"},\"endTime\":{\"type\":\"string\",\"description\":\"End time as a date string. Supports ISO format (e.g., \\\"2023-10-31\\\", \\\"2023-10-31T14:30:00\\\"), relative times (e.g., \\\"1 day ago\\\", \\\"2 weeks ago\\\", \\\"3 months ago\\\", \\\"1 year ago\\\"), and special keywords (\\\"now\\\", \\\"today\\\", \\\"yesterday\\\"). Default: current time\"},\"maxResults\":{\"type\":\"number\",\"description\":\"Maximum number of history entries to return. Use this to limit results for performance or to focus on the most relevant entries. (default: 100)\"},\"excludeCurrentTabs\":{\"type\":\"boolean\",\"description\":\"When set to true, filters out URLs that are currently open in any browser tab. Useful for finding pages you've visited but don't have open anymore. (default: false)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_history"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_history_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_bookmark_search_chrome-mcp-stdio", "description": "Search Chrome bookmarks by title and URL", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\",\"description\":\"Search query to match against bookmark titles and URLs. Leave empty to retrieve all bookmarks.\"},\"maxResults\":{\"type\":\"number\",\"description\":\"Maximum number of bookmarks to return (default: 50)\"},\"folderPath\":{\"type\":\"string\",\"description\":\"Optional folder path or ID to limit search to a specific bookmark folder. Can be a path string (e.g., \\\"Work/Projects\\\") or a folder ID.\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_bookmark_search"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_bookmark_search_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_bookmark_add_chrome-mcp-stdio", "description": "Add a new bookmark to Chrome", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to bookmark. If not provided, uses the current active tab URL.\"},\"title\":{\"type\":\"string\",\"description\":\"Title for the bookmark. If not provided, uses the page title from the URL.\"},\"parentId\":{\"type\":\"string\",\"description\":\"Parent folder path or ID to add the bookmark to. Can be a path string (e.g., \\\"Work/Projects\\\") or a folder ID. If not provided, adds to the \\\"Bookmarks Bar\\\" folder.\"},\"createFolder\":{\"type\":\"boolean\",\"description\":\"Whether to create the parent folder if it does not exist (default: false)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_bookmark_add"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_bookmark_add_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_bookmark_delete_chrome-mcp-stdio", "description": "Delete a bookmark from Chrome", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"bookmarkId\":{\"type\":\"string\",\"description\":\"ID of the bookmark to delete. Either bookmarkId or url must be provided.\"},\"url\":{\"type\":\"string\",\"description\":\"URL of the bookmark to delete. Used if bookmarkId is not provided.\"},\"title\":{\"type\":\"string\",\"description\":\"Title of the bookmark to help with matching when deleting by URL.\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_bookmark_delete"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_bookmark_delete_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "search_tabs_content_chrome-mcp-stdio", "description": "search for related content from the currently open tab and return the corresponding web pages.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"query\":{\"type\":\"string\",\"description\":\"the query to search for related content.\"}},\"required\":[\"query\"]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "search_tabs_content"}, "identifier": {"hostName": "mcpHost", "toolId": "search_tabs_content_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_inject_script_chrome-mcp-stdio", "description": "inject the user-specified content script into the webpage. By default, inject into the currently active tab", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"If a URL is specified, inject the script into the webpage corresponding to the URL.\"},\"type\":{\"type\":\"string\",\"description\":\"the javaScript world for a script to execute within. must be ISOLATED or MAIN\"},\"jsScript\":{\"type\":\"string\",\"description\":\"the content script to inject\"}},\"required\":[\"type\",\"jsScript\"]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_inject_script"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_inject_script_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_send_command_to_inject_script_chrome-mcp-stdio", "description": "if the script injected using chrome_inject_script listens for user-defined events, this tool can be used to trigger those events", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"tabId\":{\"type\":\"number\",\"description\":\"the tab where you previously injected the script(if not provided,  use the currently active tab)\"},\"eventName\":{\"type\":\"string\",\"description\":\"the eventName your injected content script listen for\"},\"payload\":{\"type\":\"string\",\"description\":\"the payload passed to event, must be a json string\"}},\"required\":[\"eventName\"]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_send_command_to_inject_script"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_send_command_to_inject_script_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}, {"definition": {"name": "chrome_console_chrome-mcp-stdio", "description": "Capture and retrieve all console output from the current active browser tab/page. This captures console messages that existed before the tool was called.", "input_schema_json": "{\"type\":\"object\",\"properties\":{\"url\":{\"type\":\"string\",\"description\":\"URL to navigate to and capture console from. If not provided, uses the current active tab\"},\"includeExceptions\":{\"type\":\"boolean\",\"description\":\"Include uncaught exceptions in the output (default: true)\"},\"maxMessages\":{\"type\":\"number\",\"description\":\"Maximum number of console messages to capture (default: 100)\"}},\"required\":[]}", "tool_safety": 0, "original_mcp_server_name": "chrome-mcp-stdio", "mcp_server_name": "chrome-mcp-stdio", "mcp_tool_name": "chrome_console"}, "identifier": {"hostName": "mcpHost", "toolId": "chrome_console_chrome-mcp-stdio"}, "isConfigured": true, "enabled": true, "toolSafety": 0}]}, {"type": "http", "name": "context7", "url": "https://mcp.context7.com/mcp", "id": "ce71985e-b1a5-4f9c-ba6d-8811f3a87ad3"}]